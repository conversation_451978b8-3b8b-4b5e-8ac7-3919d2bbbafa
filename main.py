from pyrogram import Client
import os
import yaml

# Load config from config.yaml
with open('config.yaml', 'r') as file:
    config = yaml.safe_load(file)

# Get Telegram credentials from config
api_id = config['api_credentials']['telegram']['api_id']
api_hash = config['api_credentials']['telegram']['api_hash']
session = config['api_credentials']['telegram']['session']

# Convert api_id to int if it's a string
if isinstance(api_id, str) and api_id.isdigit():
    api_id = int(api_id)

# Get proxy configuration from config
proxy = None
if config['proxy']['enabled']:
    proxy = {
        'scheme': config['proxy']['scheme'],
        'hostname': config['proxy']['hostname'],
        'port': config['proxy']['port']
    }

plugins = dict(root="plugins")

# Initialize and run the client
Client(session, api_id=api_id, api_hash=api_hash,
       proxy=proxy,
       plugins=plugins).run()
