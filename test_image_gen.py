# import google.generativeai as genai
from google import genai
from google.genai import types
import os
import wave
from pudb import set_trace

# IMPORTANT: Set your Google API key here
# You can get your API key from https://aistudio.google.com/app/apikey
API_KEY = "AIzaSyAKEefNdKMA7hjPLy5mARVT4SkDQ3H8NyE"
client = genai.Client(api_key=API_KEY)

# genai.configure(api_key=API_KEY)

def test_image_generation():
    """Tests the Google image generation API."""
    try:
        print("Generating image...")
        selected_model = 'gemini-2.5-pro'
        model = genai.GenerativeModel(selected_model)
        response = model.generate_content(
            "A beautiful landscape with mountains and a lake",
            generation_config={
                "temperature": 0.9,
                "top_p": 1,
                "top_k": 1,
                "max_output_tokens": 2048,
            },
        )

        # The response will contain the image data.
        # For this test, we will just print the response.
        print("Image generated successfully!")
        print(response)

    except Exception as e:
        print(f"An error occurred: {e}")

# Set up the wave file to save the output:
def wave_file(filename, pcm, channels=1, rate=24000, sample_width=2):
   with wave.open(filename, "wb") as wf:
      wf.setnchannels(channels)
      wf.setsampwidth(sample_width)
      wf.setframerate(rate)
      wf.writeframes(pcm)


if __name__ == "__main__":
    set_trace()
    if API_KEY == "YOUR_API_KEY":
        print("Please set your API key in the test_image_gen.py file.")
    else:
        # test_image_generation()
        response = client.models.generate_content(
        model="gemini-2.5-flash-preview-tts",
        contents="با حالت تاسف بگو: خاک بر سرت کنم موش علی",
            config=types.GenerateContentConfig(
                response_modalities=["AUDIO"],
                speech_config=types.SpeechConfig(
                    voice_config=types.VoiceConfig(
                        prebuilt_voice_config=types.PrebuiltVoiceConfig(
                        voice_name='Kore',
                        )
                    )
                ),
            )
            )

        data = response.candidates[0].content.parts[0].inline_data.data

        file_name='out.wav'
        wave_file(file_name, data) # Saves the file to current directory
