# API Credentials
api_credentials:
  telegram:
    api_id: ""  # Your Telegram API ID from https://my.telegram.org
    api_hash: "" # Your Telegram API Hash from https://my.telegram.org
    session: "my_account" # Session name

  ai_providers:
    google:
      api_key: ""  # Gemini API key from https://makersuite.google.com
    gemini:
      api_key: ""  # Gemini API key from https://makersuite.google.com
    groq:
      api_key: ""  # From https://console.groq.com
    openrouter:
      api_key: ""  # From https://openrouter.ai/keys
    cloudflare:
      account_id: ""  # From Cloudflare dashboard
      api_key: ""     # From Cloudflare dashboard

# Proxy Configuration (optional)
proxy:
  enabled: false
  scheme: "socks5"  # socks5, http, etc
  hostname: "localhost"
  port: 1234