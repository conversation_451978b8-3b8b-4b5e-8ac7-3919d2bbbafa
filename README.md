# Telegram AI Assistant Bot

A powerful Telegram userbot (self-bot) that integrates multiple AI services including Google's Gemini, Groq, Cloudflare AI, and more. Features include text generation, image generation/editing, document processing, translation, and text-to-speech capabilities.

## Features

### AI Chat & Text Generation
- Multiple AI providers support (Gemini, Groq, Cloudflare, OpenRouter)
- Context-aware chat with history
- Custom prompt modes for different use cases
- Model selection and configuration

### Document Processing
- Upload and process documents using Gemini File API
- Document summarization
- Entity extraction
- Question answering about documents
- HTML transcription
- Supports multiple file formats (PDF, Python, JavaScript, Text, etc.)

### Translation
- Multiple translation providers:
  - Gemini (AI-powered, context-aware)
  - Google Translate (fast, basic)
  - Cloudflare (M2M100 model)
  - OpenRouter (various LLMs)
- 15+ supported languages
- Custom default settings per user

### Image Generation & Editing
- Multiple models support:
  - Gemini
  - Google's Imagen
  - Cloudflare's Dreamshaper & Flux
- Image editing capabilities
- Various aspect ratios
- Multiple image generation

### Text-to-Speech
- MeloTTS integration via Cloudflare
- Multiple language support
- High-quality voice synthesis

## Prerequisites

- Python 3.8+
- MongoDB database
- Telegram API credentials (api_id and api_hash)
- API keys for AI services:
  - Google (Gemini)
  - Groq
  - Cloudflare
  - OpenRouter (optional)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/telegram-ai-assistant.git
cd telegram-ai-assistant
```

2. Create and activate virtual environment:
```bash
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Copy config template and edit:
```bash
cp config.yaml.template config.yaml
```

5. Edit `config.yaml` with your credentials:
```yaml
api_credentials:
  telegram:
    api_id: "YOUR_TELEGRAM_API_ID"
    api_hash: "YOUR_TELEGRAM_API_HASH"
  
  ai_providers:
    google:
      api_key: "YOUR_GEMINI_API_KEY"
    groq:
      api_key: "YOUR_GROQ_API_KEY"
    openrouter:
      api_key: "YOUR_OPENROUTER_API_KEY"
    cloudflare:
      account_id: "YOUR_CLOUDFLARE_ACCOUNT_ID"
      api_key: "YOUR_CLOUDFLARE_API_KEY"

proxy:
  enabled: false  # Set to true if using proxy
  scheme: "socks5"
  hostname: "localhost"
  port: 1234
```

## Usage

1. Start the bot:
```bash
python main.py
```

2. Available commands:
- `.help`: Show main help menu
- `.help translate`: Translation help
- `.help genimg`: Image generation help
- `.help profile`: Profile management help

### Basic Commands:
- `.ask <question>`: Ask AI a question
- `.chat <message>`: Chat with AI
- `.translate <text>`: Translate text
- `.genimg <prompt>`: Generate images
- `.docprocess <action>`: Process documents
- `.tts <text>`: Text to speech

## Project Structure

```
├── main.py              # Main entry point
├── config.yaml          # Configuration file
├── requirements.txt     # Python dependencies
├── plugins/            # Command plugins
│   ├── help_plug.py    # Help commands
│   ├── tools_plug.py   # Utility tools
│   ├── translate_plug.py # Translation
│   └── ...
├── helpers/            # Helper modules
│   ├── __init__.py    # Shared utilities
│   ├── config.py      # Config management
│   └── ...
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Thanks to all AI providers for their APIs
- Pyrogram for the excellent Telegram client library