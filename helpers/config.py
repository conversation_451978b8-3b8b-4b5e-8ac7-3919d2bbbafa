import yaml
import os
from typing import Any, Dict, Optional
from pathlib import Path
import logging

# Get the logger instance
logger = logging.getLogger('selfbot')

class ConfigManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            logger.info("Creating new ConfigManager instance")
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'config'):
            self.config_path = Path('config.yaml')
            logger.info(f"Initializing ConfigManager with config path: {self.config_path}")
            self.config = self._load_config()
            
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file and environment variables."""
        # Load from file
        if self.config_path.exists():
            logger.info(f"Loading configuration from {self.config_path}")
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info("Configuration file loaded successfully")
        else:
            logger.warning(f"Configuration file {self.config_path} not found, using empty config")
            config = {}
        
        # Override with environment variables if they exist
        logger.info("Checking for environment variable overrides")
        self._override_from_env(config)
        return config
    
    def _override_from_env(self, config: Dict[str, Any]) -> None:
        """Override configuration with environment variables."""
        env_mappings = {
            'API_ID': ('api_credentials', 'telegram', 'api_id'),
            'API_HASH': ('api_credentials', 'telegram', 'api_hash'),
            'SES': ('api_credentials', 'telegram', 'session'),
            'GOOGLE_API_KEY': ('api_credentials', 'ai_providers', 'google', 'api_key'),
            'GEMINI_API_KEY': ('api_credentials', 'ai_providers', 'gemini', 'api_key'),
            'GROQ_API_KEY': ('api_credentials', 'ai_providers', 'groq', 'api_key'),
            'MINITOOL_API_KEY': ('api_credentials', 'ai_providers', 'minitool', 'api_key'),
            'OPENROUTER_API_KEY': ('api_credentials', 'ai_providers', 'openrouter', 'api_key'),
            'CLOUDFLARE_ACCOUNT_ID': ('api_credentials', 'ai_providers', 'cloudflare', 'account_id'),
            'CLOUDFLARE_API_KEY': ('api_credentials', 'ai_providers', 'cloudflare', 'api_key'),
        }
        
        overridden = []
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                current = config
                for key in config_path[:-1]:
                    current = current.setdefault(key, {})
                current[config_path[-1]] = value
                overridden.append(env_var)
        
        if overridden:
            logger.info(f"Configuration overridden by environment variables: {', '.join(overridden)}")
    
    def get(self, *keys: str, default: Any = None) -> Any:
        """Get a configuration value using dot notation."""
        current = self.config
        key_path = '.'.join(keys)
        
        for key in keys:
            if isinstance(current, dict):
                current = current.get(key, default)
            else:
                logger.warning(f"Configuration path not found: {key_path}, using default: {default}")
                return default
                
        if current is None:
            logger.warning(f"No value found for {key_path}, using default: {default}")
        else:
            logger.debug(f"Retrieved configuration value for {key_path}")
        return current

    def get_telegram_credentials(self) -> Dict[str, str]:
        """Get Telegram credentials."""
        logger.info("Retrieving Telegram credentials")
        creds = {
            'api_id': self.get('api_credentials', 'telegram', 'api_id'),
            'api_hash': self.get('api_credentials', 'telegram', 'api_hash'),
            'session': self.get('api_credentials', 'telegram', 'session', default='my_account')
        }
        
        if not all([creds['api_id'], creds['api_hash']]):
            logger.error("Missing required Telegram credentials")
        return creds
    
    def get_proxy_config(self) -> Optional[Dict[str, Any]]:
        """Get proxy configuration."""
        logger.info("Retrieving proxy configuration")
        if self.get('proxy', 'enabled', default=False):
            proxy_config = {
                'scheme': self.get('proxy', 'scheme'),
                'hostname': self.get('proxy', 'hostname'),
                'port': self.get('proxy', 'port')
            }
            logger.info(f"Proxy enabled: {proxy_config['scheme']}://{proxy_config['hostname']}:{proxy_config['port']}")
            return proxy_config
        
        logger.info("Proxy disabled")
        return None

config = ConfigManager()
