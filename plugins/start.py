from pyrogram import Client, filters
from helpers import db, groq_cli
from pudb import set_trace



# @Client.on_message(filters.private & filters.me)
# async def start_handler(_, m):
#     set_trace()
#     rep = m.reply_to_message
#     txt = m.text or rep.text

#     chat_completion = groq_cli.chat.completions.create(
#     messages=[
#         {
#             "role": "user",
#             "content": f"{txt}",
#         }
#     ],
#     model="llama-3.3-70b-versatile",
#     )

#     text = chat_completion.choices[0].message.content
#     return await m.reply(text)
