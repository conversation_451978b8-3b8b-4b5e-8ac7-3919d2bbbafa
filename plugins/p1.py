# plugins/p1.py
import asyncio
import os
from pyrogram import Client, filters, errors
from helpers import db
from helpers.prompts import PROMPTS
from helpers.ai_utils import AI<PERSON><PERSON>ponse<PERSON>enerator, parse_provider
from helpers.translation_tools import ensure_language
from langdetect import detect
from pyrogram.types import Message
import unicodedata
import re
from pudb import set_trace


async def get_full_reply_chain(
    client: Client, chat_id: int, initial_message: Message
) -> list:
    """Collect the full reply chain for a given message."""
    reply_chain = []
    current_msg = initial_message
    max_depth = 10  # Prevent infinite loops

    try:
        while current_msg and max_depth > 0:
            msg_data = {
                "text": current_msg.text or current_msg.caption or "",
                "sender": (
                    current_msg.from_user.first_name
                    if current_msg.from_user
                    else "Deleted Account"
                ),
                "date": current_msg.date,
                "reply_to": current_msg.reply_to_message_id,
            }
            reply_chain.append(msg_data)

            if current_msg.reply_to_message_id:
                try:
                    prev_msg = await client.get_messages(
                        chat_id=chat_id, message_ids=current_msg.reply_to_message_id
                    )
                    current_msg = prev_msg
                except errors.MessageIdInvalid:
                    break
                except Exception as e:
                    print(
                        f"Error fetching message {current_msg.reply_to_message_id}: {str(e)}"
                    )
                    break
            else:
                break
            max_depth -= 1
        return list(reversed(reply_chain))  # Return in chronological order
    except Exception as e:
        raise Exception(f"Error collecting reply chain: {str(e)}")


async def fetch_consecutive_messages(
    client: Client, chat_id: int, start_message: Message, count: int
) -> list:
    """Fetch a specified number of consecutive messages from the same user, starting from the replied message."""
    messages = []
    user_id = start_message.from_user.id if start_message.from_user else None
    if not user_id:
        return messages  # Can't fetch if user is deleted

    # Add the current message first
    messages.append(
        {
            "text": start_message.text or start_message.caption or "",
            "sender": start_message.from_user.first_name,
            "date": start_message.date,
            "id": start_message.id,
        }
    )

    # Use search_messages to directly get messages from this specific user
    # This is more efficient than filtering through all messages
    async for msg in client.search_messages(
        chat_id,
        limit=count-1,  # -1 because we already added the start message
        from_user=user_id,  # Only get messages from this user
        offset_id=start_message.id  # Only get messages older than the start message
    ):
        # Only include messages that have text content
        if msg.text or msg.caption:
            messages.append(
                {
                    "text": msg.text or msg.caption,
                    "sender": msg.from_user.first_name if msg.from_user else "Unknown",
                    "date": msg.date,
                    "id": msg.id,
                }
            )

    # Sort messages by date (oldest first)
    sorted_messages = sorted(messages, key=lambda x: x["date"])
    return sorted_messages


@Client.on_message(
    filters.reply & filters.me & filters.command("ask", prefixes=[".", "!"])
)
async def ask_about_message(client: Client, message: Message):
    """Handle the .ask command to generate AI responses based on message context."""
    try:
        # Debug point removed
        rep = message.reply_to_message
        cmd_text = message.text  # e.g., '.ask -q rh -msg 2'
        provider = parse_provider(message.command)
        flags = dict(re.findall(r"-(\w+)\s+(\S+)", cmd_text))
        mode = list(flags.values())[0] if flags else None

        # Check for -s flag to enable search mode and remove it from cmd_text
        use_search = "-s" in cmd_text
        cmd_text = cmd_text.replace("-s", "").strip()

        if not mode or mode not in PROMPTS:
            await message.edit(
                f"❌ Invalid mode {mode}. Use `.prompts` to see available modes."
            )
            return

        # Check for flags
        get_replies_flag = "getreplies" in cmd_text

        # Check for getmsg:N parameter
        getmsg_match = re.search(r"getmsg:(\d+)", cmd_text)
        getmsg_count = None
        if getmsg_match:
            getmsg_count = int(getmsg_match.group(1))
            # Remove getmsg:N from cmd_text
            cmd_text = re.sub(r"getmsg:\d+", "", cmd_text).strip()

        # Get msg count if -msg flag exists
        msg_count = None
        if "msg" in flags:
            msg_count = flags.get("msg")
            if not msg_count.isdigit():
                return await message.edit("The message count should be integer!")
            msg_count = int(msg_count)

        cmd_text = cmd_text.replace(".ask", "")
        # Get additional context by removing flags and their values
        additional_context = re.sub(
            r"-\w+\s+\S+", "", cmd_text
        ).strip()  # Remove all -flag value pairs
        if get_replies_flag:
            additional_context = additional_context.replace("getreplies", "").strip()
        # Remove getmsg:N from additional_context if it's still there
        additional_context = re.sub(r"getmsg:\d+", "", additional_context).strip()

        # Fetch model from database
        cur = db.providers.find_one({"_id": provider})
        if not cur:
            await message.edit(f"❌ Provider {provider} not found in database.")
            return
        model = cur["default_model"]

        # Define modes requiring history
        history_mode = ("savage_roast",)

        # Initialize context and query
        context = ""
        query = ""

        # Handle getmsg:N parameter
        if getmsg_count:
            if not rep.from_user:
                await message.edit("❌ Cannot fetch messages from a deleted account.")
                return
            try:
                chat_id = message.chat.id  # Define chat_id here
                consecutive_msgs = await fetch_consecutive_messages(
                    client, chat_id, rep, getmsg_count
                )
                if not consecutive_msgs:
                    await message.edit("❌ No messages found to process.")
                    return
                # if len(consecutive_msgs) < getmsg_count:
                #     await message.edit(
                #         f"⚠️ Only found {len(consecutive_msgs)} messages instead of {getmsg_count}."
                #     )

                # Format the messages more simply - mention the sender name just once
                user_name = consecutive_msgs[0]['sender']

                # Create a list of all messages
                all_messages = [msg['text'] for msg in consecutive_msgs]

                # Create the context with a simple format that mentions the sender only once
                context = f"Messages from {user_name}:\n" + "\n".join(all_messages)
                query = context + (
                    f"\n\nAdditional context: {additional_context}"
                    if additional_context
                    else ""
                )
            except Exception as e:
                await message.edit(f"⚠️ Error fetching user messages: {str(e)}")
                return

        # Handle multiple consecutive messages
        elif msg_count:
            if not rep.from_user:
                await message.edit("❌ Cannot fetch messages from a deleted account.")
                return
            try:
                consecutive_msgs = await fetch_consecutive_messages(
                    client, message.chat.id, rep, msg_count
                )
                if not consecutive_msgs:
                    await message.edit("❌ No messages found to process.")
                    return
                if len(consecutive_msgs) < msg_count:
                    await message.edit(
                        f"⚠️ Only found {len(consecutive_msgs)} messages instead of {msg_count}."
                    )

                context_parts = []
                for msg in consecutive_msgs:
                    context_parts.append(
                        f"[{msg['date'].strftime('%Y-%m-%d %H:%M')}] {msg['sender']}:\n{msg['text']}\n―――――――――――――――――――"
                    )
                context = "\n".join(context_parts)
                query = context + (
                    f"\n\nAdditional context: {additional_context}"
                    if additional_context
                    else ""
                )
            except Exception as e:
                await message.edit(f"⚠️ Error fetching consecutive messages: {str(e)}")
                return

        # Handle reply chain mode
        elif get_replies_flag:
            try:
                chat_id = message.chat.id  # Define chat_id here
                reply_chain = await get_full_reply_chain(client, chat_id, rep)
                if not reply_chain:
                    await message.edit("❌ No reply chain found.")
                    return

                context_parts = []
                for idx, msg in enumerate(reply_chain):
                    indent = "  " * idx
                    context_parts.append(
                        f"{indent}↳ [{msg['date'].strftime('%Y-%m-%d %H:%M')}] "
                        f"{msg['sender']}:\n{indent}{msg['text']}\n"
                        f"{indent}―――――――――――――――――――"
                    )
                context = "\n".join(context_parts)
                query = context + (
                    f"\n\nAdditional context: {additional_context}"
                    if additional_context
                    else ""
                )
            except Exception as e:
                await message.edit(f"⚠️ Error processing reply chain: {str(e)}")
                return

        # Handle history analysis mode
        elif mode in history_mode:
            try:
                if not rep.from_user:
                    await message.edit("❌ Cannot analyze deleted accounts.")
                    return

                chat_id = message.chat.id  # Define chat_id here
                user_msgs = []
                async for msg in client.get_chat_history(chat_id=chat_id, limit=100):
                    if msg.text or msg.caption:
                        user_msgs.append(
                            f"[{msg.date.strftime('%H:%M')}] {msg.text or msg.caption}"
                        )

                if len(user_msgs) < 10:
                    await message.edit(
                        "🔍 At least 10 messages are required for analysis."
                    )
                    return

                context = "\n".join(user_msgs[-50:])  # Use last 50 messages
                query = context + (
                    f"\n\nAdditional context: {additional_context}"
                    if additional_context
                    else ""
                )
            except Exception as e:
                await message.edit(f"⚠️ Error collecting history: {str(e)}")
                return

        # Normal mode (single message)
        else:
            context = rep.text or rep.caption or ""
            query = context + (
                f"\n\nAdditional context: {additional_context}"
                if additional_context
                else ""
            )

            # Check if the message has a photo and provider is Google
            has_photo = rep.photo is not None
            photo_path = None
            if has_photo and provider == "google":
                try:
                    # Download the photo
                    await message.edit("📥 Downloading photo for analysis...")
                    photo_path = await rep.download()
                    await message.edit("🖼️ Photo downloaded, processing with AI...")
                except Exception as e:
                    await message.edit(f"⚠️ Error downloading photo: {str(e)}")
                    photo_path = None

        # Construct system prompt
        CONTEXT_PROMPT = f"\nThis is a conversation history to help you understand the context before responding. Read the messages carefully and ensure your response aligns with the discussion and then reply to the message id: {rep.id}."
        USER_HISTORY_PROMPT = f"\nThe following contains messages from the user you're replying to, including the current message and their previous messages. Use this context to better understand their communication style, interests, and background. This will help you provide a more personalized and relevant response that addresses their current message while considering their message history."
        SEARCH_PROMPT = "\nTo ensure accuracy, search the web to verify claims or gather up-to-date information before responding. Provide a precise and well-informed answer based on your findings."
        system_prompt = PROMPTS.get(mode, "You are a helpful assistant.")
        if detect(query) == 'fa':
            end_prompt = '\nfinal result most be in Persian(farsi) if the original message was in persian'
            end_prompt = ' All your responses must be in fluent, colloquial Persian (Farsi)'
            system_prompt += end_prompt
        if get_replies_flag or msg_count:
            system_prompt += CONTEXT_PROMPT
        if getmsg_count:
            system_prompt += USER_HISTORY_PROMPT
        if use_search:
            system_prompt += SEARCH_PROMPT

        # Normalize query
        query = unicodedata.normalize("NFC", query).strip()

        # Initialize AI generator
        ai_generator = AIResponseGenerator(provider=provider, model=model)

        # Set temperature based on mode
        temperature = (
            1.2
            if get_replies_flag or msg_count or getmsg_count
            else 0.7 if mode in ["fun", "roast"] else 0.3
        )

        # Generate response
        status_msg = (
            "🧠 Analyzing consecutive messages..."
            if msg_count
            else (
                "🧠 Analyzing user message history..."
                if getmsg_count
                else (
                    "🧠 Analyzing reply chain..."
                    if get_replies_flag
                    else (
                        "📜 Analyzing history..."
                        if mode in history_mode
                        else "💡 Generating response..."
                    )
                )
            )
        )
        try:
            await message.edit(status_msg)
        except errors.MessageNotModified:
            pass
        # await message.edit(status_msg)
        await asyncio.sleep(1)

        response_text = await ai_generator.generate_response(
            system_prompt=system_prompt,
            user_query=query,
            temperature=temperature,
            max_tokens=3000,
            use_search=use_search,
            image_path=photo_path,  # Pass the photo path if available
        )

        # Ensure response matches user's language
        if detect(query) == 'fa' and detect(response_text) != 'fa':
        # if detect(rep.text) != detect(response_text): # detect language of the reply and response
            response_text = await ensure_language(response_text, message.from_user.id, db)

        # Send response in chunks if necessary
        chunks = [
            response_text[i : i + 4096] for i in range(0, len(response_text), 4096)
        ]
        for idx, chunk in enumerate(chunks):
            if idx == 0:
                try:
                    await message.edit(chunk)
                except errors.MessageNotModified:
                    continue
            else:
                await message.reply(chunk)

        # Clean up the photo file if it was downloaded
        if photo_path and os.path.exists(photo_path):
            try:
                os.remove(photo_path)
            except Exception as e:
                print(f"Error removing temporary photo file: {str(e)}")

    except errors.MessageTooLong:
        await message.edit("📜 Response too long, please narrow down the command.")
    except Exception as e:
        await message.edit(f"⚠️ System error: {str(e)}")
