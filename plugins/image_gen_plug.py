# plugins/image_gen_plug.py
from pyrogram import Client, filters
from helpers import db, gemini_cli, gemini_safety_settings, cloudflare_ai
from google.genai import types
from PIL import Image
from io import BytesIO
import os
import base64
from pudb import set_trace

# Add Cloudflare models to help text
HELP_TEXT = """
**Image Generation Commands:**
- `.genimg <prompt>`: Generate image using default settings
- `.genimg -n <1-4> <prompt>`: Generate multiple images (Imagen only)
- `.genimg -r <ratio> <prompt>`: Use specific aspect ratio (1:1, 3:4, 4:3, 9:16, 16:9)
- `.genimg -m <model> <prompt>`: Specify model
- `.genimg describe`: Analyze and describe a replied image
- `.editimg <prompt>`: Edit replied image (Gemini or SD img2img)

**Available Models:**
- `gemini`: Google's Gemini model (default)
  • Best for: Artistic and creative images
  • Features: Can generate text explanations
  • Resolution: 1024x1024
  • Free tier available
  • May fall back to text description if image generation is unavailable

- `imagen`: Google's Imagen model
  • Best for: Photorealistic images
  • Features: Multiple image generation (1-4)
  • Supports all aspect ratios
  • Requires paid Gemini API account

**Cloudflare Models:**
- `dreamshaper`: Dreamshaper 8 LCM
  • Best for: Photorealistic and detailed images
  • Features: Fine-tuned for photorealism
  • Resolution: 1024x1024
  • By: lykon

- `flux`: FLUX.1 Schnell
  • Best for: Quick generations
  • Features: 12B parameter model
  • Resolution: 1024x1024
  • By: black-forest-labs

- `sdxl`: Stable Diffusion XL Base
  • Best for: High-quality, detailed images
  • Features: Advanced text-to-image generation
  • Resolution: 1024x1024
  • By: Stability.ai

- `sdxl-lightning`: SDXL Lightning
  • Best for: Ultra-fast, high-quality images
  • Features: Lightning-fast generation
  • Resolution: 1024x1024
  • By: bytedance

**Image Editing Models:**
- `img2img`: SD v1.5 Image-to-Image
  • For modifying existing images
  • By: runwayml

- `inpainting`: SD v1.5 Inpainting
  • For selective image editing with masks
  • By: runwayml

**Aspect Ratios:**
Available ratios: `1:1`, `3:4`, `4:3`, `9:16`, `16:9`
(Note: Some models may only support 1:1)

**Examples:**
- `.genimg a cute robot playing piano`
- `.genimg -n 4 sunset over mountains`
- `.genimg -r 16:9 cityscape at night`
- `.genimg -m sdxl professional portrait photo`
- `.genimg -m dreamshaper cyberpunk city`
- `.editimg make the background more dramatic`

**Extended Examples by Category:**

🎨 **Artistic & Creative:**
- `.genimg -m gemini a watercolor painting of cherry blossoms`
- `.genimg -m gemini vibrant abstract art with geometric shapes`
- `.genimg -m dreamshaper surreal floating islands with waterfalls`
- `.genimg magical forest with glowing mushrooms and fairies`

👤 **Professional & Portraits:**
- `.genimg -m sdxl professional linkedin headshot, business attire`
- `.genimg -m sdxl-lightning corporate team photo in modern office`
- `.genimg -m dreamshaper professional business presentation`
- `.genimg -m flux studio portrait with dramatic lighting`

🌆 **Landscapes & Environments:**
- `.genimg -m sdxl -r 16:9 epic mountain range at sunset`
- `.genimg -m dreamshaper cyberpunk city street at night`
- `.genimg -r 16:9 tropical beach with crystal clear water`
- `.genimg -m sdxl japanese garden in autumn`

🤖 **Tech & Sci-Fi:**
- `.genimg -m dreamshaper futuristic robot in a neon-lit lab`
- `.genimg -m sdxl advanced AI computer interface hologram`
- `.genimg -m flux cybernetic enhancement concept art`
- `.genimg high-tech smart home devices`

🎭 **Characters & Concepts:**
- `.genimg -m gemini cute cartoon mascot for tech startup`
- `.genimg -m dreamshaper fantasy character warrior with magic sword`
- `.genimg -m sdxl professional 3D character design`
- `.genimg steampunk inventor in workshop`

📸 **Photo Editing Examples:**
- `.editimg make the background more blurred and professional`
- `.editimg add dramatic lighting and cinematic color grading`
- `.editimg change the season to winter with snow`
- `.editimg make it look like a vintage photograph`

💡 **Pro Tips for Better Results:**
• Add details like "high quality, detailed, professional" for better output
• Specify lighting: "dramatic lighting", "soft natural light", "studio lighting"
• Include style: "photorealistic", "cinematic", "artistic", "minimalist"
• Mention camera details: "shot with DSLR", "85mm lens", "bokeh effect"
• Use aspect ratio for specific needs: -r 16:9 for landscapes, -r 3:4 for portraits
"""

VALID_RATIOS = ["1:1", "3:4", "4:3", "9:16", "16:9"]

@Client.on_message(filters.me & filters.command("genimg", prefixes=[".", "!"]))
async def generate_image(client, message):
    status_message = None
    set_trace()
    try:
        # Parse command arguments
        cmd = message.text.split()
        if len(cmd) < 2:
            await message.reply(HELP_TEXT)
            return

        # Initialize variables
        model = "gemini-1.5-pro"  # Use Gemini Pro for image description by default
        provider = 'google'

        # List of Gemini models to try for image generation, in order of preference
        num_images = 1
        aspect_ratio = "1:1"
        prompt_start = 1
        describe_mode = False  # Flag to indicate if we're describing an image

        # Add Cloudflare model mapping
        cloudflare_models = {
            'dreamshaper': '@cf/lykon/dreamshaper-8-lcm',
            'flux': '@cf/black-forest-labs/flux-1-schnell',
            'sdxl': '@cf/stabilityai/stable-diffusion-xl-base-1.0',  # Updated path
            'sdxl-lightning': '@cf/bytedance/stable-diffusion-xl-lightning',
            'img2img': '@cf/runwayml/stable-diffusion-v1-5-img2img',
            'inpainting': '@cf/runwayml/stable-diffusion-v1-5-inpainting'
        }

        # Parse arguments
        while prompt_start < len(cmd):
            if cmd[prompt_start] == '-m' and prompt_start + 1 < len(cmd):
                model_key = cmd[prompt_start + 1].lower()
                if model_key in cloudflare_models:
                    model = cloudflare_models[model_key]
                    provider = 'cloudflare'
                elif model_key == 'gemini':
                    model = "gemini"  # Just a marker, we'll try multiple models
                    provider = 'google'
                elif model_key == 'imagen':
                    model = "imagen-3.0-generate-002"
                    provider = 'google'
                    # Add a warning about paid tier requirement
                    await message.reply("⚠️ Note: The Imagen API requires a paid Gemini API account. If you're on the free tier, this may fail.")
                else:
                    await message.reply("⚠️ Invalid model. Use 'gemini', 'imagen', 'dreamshaper', 'flux', 'sdxl', or 'sdxl-lightning'")
                    return
                prompt_start += 2
            elif cmd[prompt_start].lower() == 'describe':
                # If the command is 'describe', set the flag and move to the next argument
                describe_mode = True
                prompt_start += 1
            else:
                break

        prompt = " ".join(cmd[prompt_start:])

        # Check if we're in describe mode and if there's a replied message with a photo
        if describe_mode:
            if not message.reply_to_message or not message.reply_to_message.photo:
                await message.reply("⚠️ Please reply to an image to describe it")
                return
            status_message = await message.reply("🔍 Analyzing image...")

            # Download and process the image
            photo_path = await message.reply_to_message.download()
            image = Image.open(photo_path)

            # Use Gemini to describe the image
            try:
                response = googe_cli.models.generate_content(
                    model=model,  # Use Gemini Pro for image description
                    contents=[image],
                    config=types.GenerateContentConfig(
                        response_modalities=['TEXT'],
                        system_instruction="Describe this image in detail. Include information about what's in the image, colors, objects, people, setting, and any text visible."
                    )
                )

                # Reply with the description
                await message.reply_to_message.reply(f"📝 **Image Description:**\n\n{response.text}")

                # Clean up the downloaded image
                os.remove(photo_path)

                # Clean up status message
                if status_message:
                    try:
                        await status_message.delete()
                    except:
                        pass
                return  # Exit the function after describing the image
            except Exception as e:
                await message.reply(f"⚠️ Error analyzing image: {str(e)}")
                return
        else:
            status_message = await message.reply("🎨 Generating image(s)...")

        if provider == 'cloudflare':
            try:
                if model == '@cf/stabilityai/stable-diffusion-xl-base-1.0':
                    messages = {
                        "prompt": prompt,
                        "negative_prompt": "blurry, bad quality, distorted, ugly, bad anatomy, worst quality, low quality",
                        "num_steps": 20,
                        "guidance": 7.5,
                        "width": 1024,
                        "height": 1024
                    }
                elif model == '@cf/black-forest-labs/flux-1-schnell':
                    messages = {
                        "prompt": prompt,
                        "steps": 6,
                        "negative_prompt": "blurry, distorted, cartoon, illustration, low quality, deformed, ugly, bad anatomy",
                        "seed": -1,
                        "num_inference_steps": 8,
                        "guidance_scale": 7.5,
                        "width": 1024,
                        "height": 1024
                    }
                elif model == '@cf/bytedance/stable-diffusion-xl-lightning':
                    messages = {
                        "prompt": prompt,
                        "num_steps": 20,
                        "guidance": 7.5,
                        "negative_prompt": "blurry, bad quality, distorted, ugly, bad anatomy",
                        "width": 1024,
                        "height": 1024
                    }
                else:
                    messages = {
                        "prompt": prompt,
                        "parameters": {
                            "width": 1024,
                            "height": 1024,
                            "num_inference_steps": 20,
                            "guidance_scale": 7.5,
                            "negative_prompt": "blurry, bad quality, distorted"
                        }
                    }

                response = cloudflare_ai.run_model(
                    model=model,
                    messages=messages
                )

                # Handle different response types
                if isinstance(response, (bytes, bytearray)):
                    image_data = response
                elif isinstance(response, dict):
                    if 'image' in response:
                        image_data = base64.b64decode(response['image'])
                    elif 'data' in response:
                        image_data = base64.b64decode(response['data'])
                    elif 'result' in response and isinstance(response['result'], dict):
                        if 'image' in response['result']:
                            image_data = base64.b64decode(response['result']['image'])
                        elif 'data' in response['result']:
                            image_data = base64.b64decode(response['result']['data'])
                    else:
                        raise ValueError("Failed to extract image data from response")
                else:
                    raise ValueError("Unexpected response format")

                # Convert image data to PIL Image and save
                image = Image.open(BytesIO(image_data))
                image_path = "generated_image.png"
                image.save(image_path)
                await message.reply_photo(image_path)
                os.remove(image_path)

            except Exception as e:
                error_msg = f"⚠️ Generation failed: {str(e)}"
                if status_message:
                    try:
                        await status_message.edit(error_msg)
                    except:
                        await message.reply(error_msg)
                return

        elif provider == 'google':
            if model == "gemini" or model.startswith("gemini"):
                # Try multiple Gemini models for image generation
                success = False
                last_error = None
                cur = db.providers.find_one({"_id": provider})
                if not cur:
                    await message.edit(f"❌ Provider {provider} not found in database.")
                    return
                model = cur["default_model"]

                gemini_image_models = [
                    model,
                    "gemini-2.0-flash-exp-image-generation",  # Try the experimental model first
                    "gemini-1.5-flash",                      # Then try the standard models
                    "gemini-1.5-pro",
                ]

                for current_model in gemini_image_models:
                    try:
                        if status_message:
                            await status_message.edit_text(f"🎨 Trying to generate image with {current_model}...")

                        # Use the current model from the list
                        response = googe_cli.models.generate_content(
                            model=current_model,
                            contents=prompt,
                            config=types.GenerateContentConfig(
                                response_modalities=['TEXT', 'IMAGE'],  # Must use uppercase as per documentation
                            )
                        )

                        image_found = False
                        for i, part in enumerate(response.candidates[0].content.parts):
                            if part.inline_data is not None:
                                image_found = True
                                image = Image.open(BytesIO(part.inline_data.data))
                                image_path = f"generated_image_{i}.png"
                                image.save(image_path)
                                await message.reply_photo(image_path)
                                os.remove(image_path)
                            elif part.text:
                                await message.reply(part.text)

                        # If we found an image, mark as success and break the loop
                        if image_found:
                            success = True
                            break

                        # If no image was found in the response, try with a more explicit prompt
                        if not image_found:
                            if status_message:
                                await status_message.edit_text("🎨 No image was generated. Trying again with a more explicit prompt...")
                            enhanced_prompt = f"Generate an image of {prompt}. Make sure to include an image in your response."
                            response = googe_cli.models.generate_content(
                                model=current_model,
                                contents=enhanced_prompt,
                                config=types.GenerateContentConfig(
                                    response_modalities=['TEXT', 'IMAGE'],
                                )
                            )

                            for i, part in enumerate(response.candidates[0].content.parts):
                                if part.inline_data is not None:
                                    image_found = True
                                    image = Image.open(BytesIO(part.inline_data.data))
                                    image_path = f"generated_image_{i}.png"
                                    image.save(image_path)
                                    await message.reply_photo(image_path)
                                    os.remove(image_path)
                                elif part.text:
                                    await message.reply(part.text)

                            # If we found an image with the enhanced prompt, mark as success and break the loop
                            if image_found:
                                success = True
                                break

                    except Exception as e:
                        last_error = str(e)
                        continue  # Try the next model

                # If none of the models worked
                if not success:
                    if last_error:
                        await message.reply(f"⚠️ Error: Could not generate image with any Gemini model. Please try using a Cloudflare model instead with `-m sdxl` or `-m dreamshaper`.\n\nLast error: {last_error}")
                    else:
                        await message.reply("⚠️ Error: Could not generate image with any Gemini model. Please try using a Cloudflare model instead with `-m sdxl` or `-m dreamshaper`.")
            else:
                # Fix the GenerateImagesConfig to not include safety_settings
                try:
                    response = googe_cli.models.generate_images(
                        model=model,
                        prompt=prompt,
                        config=types.GenerateImagesConfig(
                            number_of_images=num_images,
                            aspect_ratio=aspect_ratio,
                            # safety_settings parameter is not supported in the current API version
                        )
                    )
                except Exception as e:
                    error_message = str(e)
                    if "billed users" in error_message:
                        await message.reply("⚠️ Error: The Imagen API requires a paid Gemini API account. Please use `-m gemini` instead or upgrade your account.")
                    else:
                        await message.reply(f"⚠️ Error generating image: {error_message}")
                    return

                for i, generated_image in enumerate(response.generated_images):
                    image = Image.open(BytesIO(generated_image.image.image_bytes))
                    image_path = f"generated_image_{i}.png"
                    image.save(image_path)
                    await message.reply_photo(image_path)
                    os.remove(image_path)

        # Clean up status message
        if status_message:
            try:
                await status_message.delete()
            except:
                pass

    except Exception as e:
        if status_message:
            try:
                await status_message.edit(f"⚠️ Error: {str(e)}")
            except:
                await message.reply(f"⚠️ Error: {str(e)}")

@Client.on_message(filters.me & filters.command("editimg", prefixes=[".", "!"]))
async def edit_image(_, message):
    try:
        if not message.reply_to_message or not message.reply_to_message.photo:
            await message.edit("⚠️ Please reply to an image to edit it")
            return

        # Parse command arguments
        cmd = message.text.split()
        model = "gemini"  # default model
        prompt_start = 1
        strength = 0.8  # Default strength for img2img
        num_steps = 20
        guidance = 7.5

        # Parse arguments
        i = 1
        while i < len(cmd):
            if cmd[i] == '-m':
                model = cmd[i + 1].lower()
                i += 2
            elif cmd[i] == '-s':  # Add strength parameter
                strength = float(cmd[i + 1])
                i += 2
            elif cmd[i] == '-n':  # Add num_steps parameter
                num_steps = min(int(cmd[i + 1]), 20)  # Max 20 steps
                i += 2
            elif cmd[i] == '-g':  # Add guidance parameter
                guidance = float(cmd[i + 1])
                i += 2
            else:
                prompt_start = i
                break

        if len(cmd) <= prompt_start:
            await message.edit("⚠️ Please provide editing instructions")
            return

        prompt = " ".join(cmd[prompt_start:])
        await message.edit("🎨 Editing image...")

        # Download and process the image
        photo_path = await message.reply_to_message.download()
        image = Image.open(photo_path)

        if model in ["img2img", "inpainting"]:
            # Convert image to base64
            buffered = BytesIO()
            image.save(buffered, format="PNG")
            img_base64 = base64.b64encode(buffered.getvalue()).decode()

            # Prepare model path
            model_path = {
                "img2img": "@cf/runwayml/stable-diffusion-v1-5-img2img",
                "inpainting": "@cf/runwayml/stable-diffusion-v1-5-inpainting"
            }[model]

            # Prepare messages for Cloudflare API
            messages = {
                "prompt": prompt,
                "image_b64": img_base64,  # Use image_b64 instead of image
                "negative_prompt": "blurry, bad quality, distorted, ugly, bad anatomy",
                "num_steps": num_steps,
                "strength": strength,
                "guidance": guidance,
                "width": 1024,
                "height": 1024
            }

            # Generate edited image using Cloudflare
            response = cloudflare_ai.run_model(
                model=model_path,
                messages=messages
            )

            # Save and send the response
            if isinstance(response, bytes):
                # Direct binary response
                edited_image = Image.open(BytesIO(response))
            else:
                # Handle potential different response format
                edited_image = Image.open(BytesIO(response.content))

            edited_path = "edited_image.png"
            edited_image.save(edited_path)
            await message.reply_to_message.reply_photo(
                edited_path,
                caption=f"🎨 Edited with {model}\nPrompt: {prompt}\nStrength: {strength}\nSteps: {num_steps}\nGuidance: {guidance}"
            )
            os.remove(edited_path)

        else:  # Use Gemini as default
            response = googe_cli.models.generate_content(
                model="gemini-2.0-flash-exp-image-generation",
                contents=[prompt, image],
                config=types.GenerateContentConfig(
                    response_modalities=['Text', 'Image'],
                    safety_settings=gemini_safety_settings,
                )
            )

            for part in response.candidates[0].content.parts:
                if part.inline_data is not None:
                    edited_image = Image.open(BytesIO(part.inline_data.data))
                    edited_path = "edited_image.png"
                    edited_image.save(edited_path)
                    await message.reply_to_message.reply_photo(
                        edited_path,
                        caption=f"🎨 Edited with Gemini\nPrompt: {prompt}"
                    )
                    os.remove(edited_path)
                elif part.text:
                    await message.reply_to_message.reply(part.text)

        os.remove(photo_path)
        await message.delete()

    except Exception as e:
        await message.edit(f"⚠️ Error editing image: {str(e)}")
