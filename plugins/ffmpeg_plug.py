# plugins/ffmpeg_plug.py
from pyrogram import Client, filters
from helpers import db
from helpers.prompts import PROMPTS
from helpers.ai_utils import AIResponseGenerator, parse_provider
import os
import asyncio
import subprocess
import re
from pyrogram.types import Message
from pudb import set_trace


@Client.on_message(
    filters.reply & filters.me & filters.command("ffmpeg", prefixes=[".", "!"])
)
async def ffmpeg_process(client: Client, message: Message):
    input_path = None
    output_path = None
    try:
        rep = message.reply_to_message
        cmd = message.command

        # Check if replying to a valid media file
        if not (rep.audio or rep.voice or rep.video or rep.animation):
            await message.edit("❌ Please reply to an audio, video, or GIF file.")
            return

        # Parse provider and remaining command
        provider = parse_provider(cmd)
        if not remaining_cmd:
            await message.edit(
                "❌ Please specify an FFmpeg instruction. Example: `.ffmpeg -q speed up by 2x`"
            )
            return

        # Fetch model from database
        cur = db.providers.find_one({"_id": provider})
        if not cur:
            await message.edit(f"❌ Provider {provider} not found in database.")
            return
        model = cur["default_model"]

        # Determine file type and name
        if rep.audio:
            file_name = rep.audio.file_name or "input.mp3"
            base_ext = "mp3"
        elif rep.video:
            file_name = rep.video.file_name or "input.mp4"
            base_ext = "mp4"
        elif rep.animation:
            file_name = rep.animation.file_name or "input.gif"
            base_ext = "gif"
        elif rep.voice:
            # file_name = rep.voice.file_name or "input.ogg"
            file_name = "input.ogg"
            base_ext = "ogg"

        # Download the file and get the full path
        await message.edit("📥 Downloading file...")
        input_path = await rep.download()
        input_path = os.path.abspath(input_path)  # Ensure full absolute path

        # Construct query with full input path and instructions
        query = f"Input file: {input_path}\nInstructions: {' '.join(remaining_cmd)}"
        ai_generator = AIResponseGenerator(provider=provider, model=model)

        # Generate FFmpeg command
        await message.edit("🧠 Generating FFmpeg command...")
        ffmpeg_command = await ai_generator.generate_response(
            system_prompt=PROMPTS["ffmpeg_command"],
            user_query=query,
            temperature=0.3,  # Low temperature for precise commands
        )

        if model.startswith("deepseek-r1"):
            ffmpeg_command = ffmpeg_command.split("</think>", 1)[-1].strip()
        # Validate and clean FFmpeg command
        ffmpeg_command = validate_ffmpeg_command(ffmpeg_command, input_path)
        if not ffmpeg_command:
            await message.edit(
                "❌ Invalid or unclear FFmpeg command generated. Please try again."
            )
            return

        # Determine output extension and path
        output_extension = determine_output_extension(ffmpeg_command, base_ext, query)
        output_path = os.path.abspath(
            f"output.{output_extension}"
        )  # Full path for output

        # Adjust command with actual output path
        ffmpeg_command = adjust_ffmpeg_command(ffmpeg_command, input_path, output_path)

        # Execute FFmpeg command
        await message.edit(f"⚙️ Running FFmpeg command: `{ffmpeg_command}`...")
        process = await asyncio.create_subprocess_shell(
            ffmpeg_command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await process.communicate()

        if process.returncode != 0:
            error_msg = stderr.decode().strip()
            await message.edit(f"❌ FFmpeg error: {error_msg[:1000]}")
            return

        # Upload the processed file
        await message.edit("📤 Uploading processed file...")
        if output_extension in ["mp3", "wav", "ogg"]:
            await client.send_audio(
                chat_id=message.chat.id,
                audio=output_path,
                reply_to_message_id=message.id,
            )
        elif output_extension in ["mp4", "avi", "mov"]:
            await client.send_video(
                chat_id=message.chat.id,
                video=output_path,
                reply_to_message_id=message.id,
            )
        elif output_extension in ["gif"]:
            await client.send_animation(
                chat_id=message.chat.id,
                animation=output_path,
                reply_to_message_id=message.id,
            )
        else:
            await client.send_document(
                chat_id=message.chat.id,
                document=output_path,
                reply_to_message_id=message.id,
            )

        await message.edit("✅ Processing and upload completed successfully!")

    except Exception as e:
        await message.edit(f"⚠️ FFmpeg processing error: {str(e)}")
    finally:
        # Clean up files
        if input_path and os.path.exists(input_path):
            os.remove(input_path)
        if output_path and os.path.exists(output_path):
            os.remove(output_path)


def validate_ffmpeg_command(command: str, input_path: str) -> str:
    """Validate and clean the FFmpeg command."""
    if not command or "Clarify your request" in command:
        return None

    # Remove extra newlines, trailing punctuation, and normalize spacing
    command = command.strip().rstrip("\n.,")
    command = re.sub(r"\s+", " ", command)

    # Ensure command starts with 'ffmpeg' and has '-i'
    if not command.startswith("ffmpeg") or "-i" not in command:
        return None

    # Replace any generic input file reference with the actual input path
    command = re.sub(r'-i\s+[^\'"\s]+', f'-i "{input_path}"', command)

    # Basic check for output file presence (output.*)
    if not re.search(r"output\.\w+", command):
        return None

    return command


def determine_output_extension(command: str, default_ext: str, query: str) -> str:
    """Determine the output file extension from the command, query, or default."""
    if "convert to" in query.lower():
        match = re.search(r"convert to (\w+)", query.lower())
        if match:
            return match.group(1)

    match = re.search(r"output\.(\w+)", command)
    if match:
        return match.group(1)

    return default_ext


def adjust_ffmpeg_command(command: str, input_path: str, output_path: str) -> str:
    """Adjust the FFmpeg command with actual input and output paths."""
    # Replace input placeholders with full path (though validator should handle this)
    command = (
        command.replace("input.gif", input_path)
        .replace("input.mp3", input_path)
        .replace("input.mp4", input_path)
    )
    # Replace output placeholder with full path, ensuring no duplicates
    command = re.sub(r"output\.\w+", output_path, command)
    return command


# # plugins/ffmpeg_plug.py
# from pyrogram import Client, filters
# from helpers import db
# from helpers.prompts import PROMPTS
# from helpers.ai_utils import AIResponseGenerator, parse_provider_and_args
# import os
# import asyncio
# import subprocess
# from pyrogram.types import Message
# from pudb import set_trace

# @Client.on_message(filters.reply & filters.me & filters.command('ffmpeg', prefixes=['.', '!']))
# async def ffmpeg_process(client: Client, message: Message):
#     input_path = None
#     output_path = None
#     try:
#         rep = message.reply_to_message
#         cmd = message.command
#         set_trace()
#         # Check if replying to a valid media file
#         if not (rep.audio or rep.video or rep.animation):
#             await message.edit("❌ Please reply to an audio, video, or GIF file.")
#             return

#         # Parse provider and remaining command
#         provider, remaining_cmd = parse_provider_and_args(cmd)
#         if not remaining_cmd:
#             await message.edit("❌ Please specify an FFmpeg instruction. Example: `.ffmpeg -q speed up by 2x`")
#             return

#         # Fetch model from database
#         cur = db.providers.find_one({'_id': provider})
#         if not cur:
#             await message.edit(f"❌ Provider {provider} not found in database.")
#             return
#         model = cur['default_model']

#         # Determine file type and name
#         if rep.audio:
#             file_name = rep.audio.file_name or "input.mp3"
#             mime_type = rep.audio.mime_type
#         elif rep.video:
#             file_name = rep.video.file_name or "input.mp4"
#             mime_type = rep.video.mime_type
#         elif rep.animation:
#             file_name = rep.animation.file_name or "input.gif"
#             mime_type = rep.animation.mime_type

#         # Download the file
#         await message.edit("📥 Downloading file...")
#         input_path = await rep.download()

#         # Construct query and initialize AI generator
#         query = ' '.join(remaining_cmd)
#         ai_generator = AIResponseGenerator(provider=provider, model=model)

#         # Generate FFmpeg command
#         await message.edit("🧠 Generating FFmpeg command...")
#         ffmpeg_command = await ai_generator.generate_response(
#             system_prompt=PROMPTS['ffmpeg_command'],
#             user_query=query,
#             temperature=0.3  # Low temperature for precise commands
#         )

#         # Validate FFmpeg command
#         if not ffmpeg_command or "Clarify your request" in ffmpeg_command:
#             await message.edit("❌ FFmpeg command unclear. Please clarify your request.")
#             return

#         # Prepare output file path
#         output_extension = file_name.split('.')[-1]
#         if "convert to" in query.lower():
#             new_format = query.split("convert to")[-1].strip().split()[0]
#             output_extension = new_format
#         output_path = f"output.{output_extension}"

#         # Adjust command with actual file paths
#         ffmpeg_command = ffmpeg_command.replace('input.gif', input_path).replace('input.mp3', input_path).replace('input.mp4', input_path)
#         ffmpeg_command = ffmpeg_command.replace('output', output_path)
#         if model.startswith('deepseek-r1'):
#             ffmpeg_command = ffmpeg_command.split('</think>', 1)[-1].strip()

#         # Execute FFmpeg command
#         await message.edit(f"⚙️ Running FFmpeg command: `{ffmpeg_command}`...")
#         process = await asyncio.create_subprocess_shell(
#             ffmpeg_command,
#             stdout=asyncio.subprocess.PIPE,
#             stderr=asyncio.subprocess.PIPE
#         )
#         stdout, stderr = await process.communicate()

#         if process.returncode != 0:
#             error_msg = stderr.decode().strip()
#             await message.edit(f"❌ FFmpeg error: {error_msg[:1000]}")
#             return

#         # Upload the processed file
#         await message.edit("📤 Uploading processed file...")
#         if output_extension in ['mp3', 'wav', 'ogg']:
#             await client.send_audio(chat_id=message.chat.id, audio=output_path, reply_to_message_id=message.message_id)
#         elif output_extension in ['mp4', 'avi', 'mov']:
#             await client.send_video(chat_id=message.chat.id, video=output_path, reply_to_message_id=message.message_id)
#         elif output_extension in ['gif']:
#             await client.send_animation(chat_id=message.chat.id, animation=output_path, reply_to_message_id=message.message_id)
#         else:
#             await client.send_document(chat_id=message.chat.id, document=output_path, reply_to_message_id=message.message_id)

#         await message.edit("✅ Processing and upload completed successfully!")

#     except Exception as e:
#         await message.edit(f"⚠️ FFmpeg processing error: {str(e)}")
#     finally:
#         # Clean up files
#         if input_path and os.path.exists(input_path):
#             os.remove(input_path)
#         if output_path and os.path.exists(output_path):
#             os.remove(output_path)
