from pyrogram import Client, filters, errors


@Client.on_message(filters.me & filters.command(['clearmychat', 'cleanmychat'], prefixes=['.', '!']))
async def delete_my_messages(client, message):
    try:
        # Start deletion process immediately
        await message.edit("🧹 Starting message deletion...")

        deleted_count = 0
        async for msg in client.search_messages(
            chat_id=message.chat.id,
            from_user="me"
        ):
            try:
                await msg.delete()
                deleted_count += 1

                # Update status every 10 messages
                if deleted_count % 10 == 0:
                    await message.edit(f"🧹 Deleted {deleted_count} messages so far...")

            except errors.MessageDeleteForbidden:
                await message.edit("❌ Cannot delete messages in this chat (permission denied)")
                return
            except Exception as e:
                print(f"Error deleting message {msg.id}: {str(e)}")
                continue

        await message.edit(f"✅ Successfully deleted {deleted_count} messages!")

    except Exception as e:
        await message.edit(f"⚠️ Error: {str(e)}")
