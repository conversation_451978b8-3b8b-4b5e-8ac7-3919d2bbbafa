from pyrogram import Client, filters
from helpers import db
from helpers.translation_tools import ensure_language
from pudb import set_trace

VALID_PROVIDERS = {
    '-g': 'gemini', 
    '-d': 'deepseek-r1', 
    '-t': 'googletrans',
    '-c': 'cloudflare'  # Add Cloudflare option
}
VALID_LANGUAGES = ['fa', 'en', 'ar', 'tr', 'de', 'es', 'it', 'ja', 'ko', 'zh', 'hi', 'ru', 'nl', 'pt', 'fr']

@Client.on_message(filters.me & filters.command('translate', prefixes=['.', '!']))
async def translate_message(client, message):
    """
    Handle translation commands in different formats:
    1. .translate text - Translates text using default settings
    2. .translate -g text - Translates using Gemini
    3. .translate en text - Translates to English
    4. .translate en -g text - Translates to English using Gemini
    """
    try:
        cmd = message.command
        reply = message.reply_to_message

        if len(cmd) < 2:
            await message.edit(
                "Usage:\n"
                "`.translate text` - Translate using default settings\n"
                "`.translate -g/-d/-t text` - Use specific provider\n"
                "`.translate lang text` - Translate to specific language\n"
                "`.translate lang -g text` - Combine language and provider"
            )
            return

        # Initialize default values from user preferences
        user_prefs = db.translation_prefs.find_one({'user_id': message.from_user.id}) or {}
        target_lang = user_prefs.get('target_lang', 'fa')
        translate_model = user_prefs.get('translate_model', 'googletrans')

        # Get text to translate
        if reply:
            if message.quote:
                text = message.quote.text
            else:
                text = reply.text or reply.caption
            if not text:
                return await message.edit("❌ No text found in replied message")
            cmd_start = 1  # Skip command name
        else:
            text = message.text or message.caption
            if not text:
                return await message.edit("❌ No text provided")
            # Skip command and get remaining text
            text = text.split(None, 1)[1]
            cmd_start = 0  # We already extracted text

        # Parse command arguments
        args = cmd[cmd_start:]
        
        # Check for provider flag
        if args and args[0] in VALID_PROVIDERS:
            translate_model = VALID_PROVIDERS[args[0]]
            args.pop(0)
        elif args and args[0] in VALID_LANGUAGES:
            target_lang = args[0]
            args.pop(0)
            # Check for provider after language
            if args and args[0] in VALID_PROVIDERS:
                translate_model = VALID_PROVIDERS[args[0]]
                args.pop(0)

        # If we're processing a reply and have remaining args, use them as text
        if reply and args:
            text = ' '.join(args)

        # Validate language code
        if target_lang not in VALID_LANGUAGES:
            return await message.edit(f"❌ Invalid language code. Valid codes: {', '.join(VALID_LANGUAGES)}")

        await message.edit("🔄 Translating...")
        
        # Perform translation
        translated_text = await ensure_language(
            text=text,
            user_id=message.from_user.id,
            db=db,
            default_lang=target_lang,
            translate_model=translate_model
        )

        await message.edit(translated_text)

    except Exception as e:
        await message.edit(f"❌ Translation error: {str(e)}")
    
