from pyrogram import Client, filters
from helpers import db
from helpers.models import providers
from pudb import set_trace



@Client.on_message(filters.command('setmodel', prefixes=['.', '!']) & filters.me)
async def set_model(client, message):
    try:
        cmd = message.command
        if len(cmd) < 3:
            await message.edit("❌ Please specify a provider and model name. Example: `.setmodel groq llama-3.3-70b-versatile`")
            return

        provider = cmd[1].lower()
        model_name = cmd[2]

        # Validate provider
        valid_providers = list(providers.keys())  # Get providers from the models dictionary
        if provider not in valid_providers:
            await message.edit(f"❌ Invalid provider. Use one of: {', '.join(valid_providers)}")
            return

        # Validate model
        available_models = providers[provider]['models']
        if model_name not in available_models:
            await message.edit(f"❌ Invalid model. Available models for `{provider}`: {', '.join(available_models)}")
            return

        # Update the default model in the database
        db.providers.update_one(
            {'_id': provider},
            {'$set': {'default_model': model_name}},
            upsert=True
        )

        await message.edit(f"✅ Default model for `{provider}` set to `{model_name}` successfully.")
    except Exception as e:
        await message.edit(f"⚠️ Error setting default model: {str(e)}")



@Client.on_message(filters.command("save", prefixes=[".", "!"]) & filters.me)
async def save_providers(client, message):
    try:
        # Convert providers dictionary to a list of documents
        docs = [{'_id': provider, **data} for provider, data in providers.items()]

        # Use insert_many to add or replace documents, or update_many with individual updates
        # Option 1: Clear and insert (simpler)
        db.providers.delete_many({})  # Optional: Clear existing data
        db.providers.insert_many(docs)

        # Option 2: Update existing documents (if you want to preserve other fields)
        # for doc in docs:
        #     db.providers.update_one(
        #         {'_id': doc['_id']},
        #         {'$set': {'default_model': doc['default_model'], 'models': doc['models']}},
        #         upsert=True
        #     )

        await message.edit("✅ Providers and models saved to MongoDB!")
    except Exception as e:
        await message.edit(f"⚠️ Error: {str(e)}")
