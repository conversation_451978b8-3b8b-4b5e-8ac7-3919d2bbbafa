#!/usr/bin/env python3
import os
from google import genai

# Get API key from environment or config
api_key = os.environ.get('GOOGLE_API_KEY')
if not api_key:
    # Try to read from config file
    try:
        import json
        with open('config.json', 'r') as f:
            config = json.load(f)
            api_key = config.get('api_credentials', {}).get('ai_providers', {}).get('google', {}).get('api_key')
    except Exception as e:
        print(f"Error reading config: {e}")

if not api_key:
    print("No Google API key found. Please set GOOGLE_API_KEY environment variable or update config.json")
    exit(1)

# Initialize the client
client = genai.Client(api_key=api_key)

try:
    # List available models
    models = client.models.list()
    print("Available Gemini models:")
    for model in models:
        print(f"- {model.name}")
        # Print model details if available
        if hasattr(model, 'display_name'):
            print(f"  Display name: {model.display_name}")
        if hasattr(model, 'description'):
            print(f"  Description: {model.description}")
        print()
except Exception as e:
    print(f"Error listing models: {e}")
